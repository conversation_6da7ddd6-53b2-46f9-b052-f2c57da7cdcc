from fastapi import APIRouter, HTTPException, Path, Body

from app.controllers.bundle import bundle_controller
from app.models.enums import BundleType
from app.schemas.base import Success, Fail
from app.schemas.knowledge_base import (
    KnowledgeBaseQueryRequest,
    KnowledgeBaseQueryResponse,
    KnowledgeBaseBuildResponse,
    KnowledgeBaseStatusResponse
)
from app.ai.kb.manager import KnowledgeBaseManager
from app.log import logger

router = APIRouter()


@router.post("/bundles/{bundle_id}/build", summary="构建知识库")
async def build_knowledge_base(
    bundle_id: int = Path(..., description="Bundle ID", ge=1)
):
    """
    构建指定Bundle的知识库
    
    - **bundle_id**: Bundle ID，必须是knowledge_base类型
    - 会遍历Bundle下所有已完成OCR的页面，将ocr_result插入到知识库中
    """
    try:
        # 验证Bundle存在且类型正确
        bundle = await bundle_controller.get_bundle_info(bundle_id)
        if bundle.bundle_type != BundleType.KNOWLEDGE_BASE:
            return Fail(code=400, msg=f"Bundle {bundle_id} is not a knowledge base type")
        
        # 获取RAG引擎配置
        from app.models.bundle import Bundle
        from app.controllers.education.bundle_service import BundleService
        bundle_model = await Bundle.get(id=bundle_id)
        config = BundleService._get_kb_config(bundle_model)

        # 获取RAG引擎实例
        rag_engine = await KnowledgeBaseManager.get_rag_engine(bundle_id, config)

        # 构建知识库
        success = await BundleService.build_knowledge_base(bundle_id, rag_engine)
        
        if success:
            # 获取构建结果
            updated_bundle = await Bundle.get(id=bundle_id)
            metadata = updated_bundle.custom_metadata or {}
            
            response = KnowledgeBaseBuildResponse(
                bundle_id=bundle_id,
                document_count=metadata.get("document_count", 0),
                status=metadata.get("status", "unknown")
            )
            return Success(data=response, msg="知识库构建成功")
        else:
            return Fail(code=500, msg="知识库构建失败")
            
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.error(f"Error building knowledge base for bundle {bundle_id}: {e}")
        return Fail(code=500, msg=f"构建知识库时发生错误: {str(e)}")


@router.post("/bundles/{bundle_id}/query", summary="查询知识库")
async def query_knowledge_base(
    bundle_id: int = Path(..., description="Bundle ID", ge=1),
    request: KnowledgeBaseQueryRequest = Body(...)
):
    """
    查询指定Bundle的知识库
    
    - **bundle_id**: Bundle ID，必须是knowledge_base类型且已构建
    - **query**: 查询问题
    - **mode**: 查询模式 (naive, local, global, hybrid)
    """
    try:
        # 验证Bundle存在且类型正确
        bundle = await bundle_controller.get_bundle_info(bundle_id)
        if bundle.bundle_type != BundleType.KNOWLEDGE_BASE:
            return Fail(code=400, msg=f"Bundle {bundle_id} is not a knowledge base type")
        
        # 查询知识库
        from app.controllers.education.bundle_service import BundleService
        result = await BundleService.query_knowledge_base(
            bundle_id,
            request.query,
            request.mode
        )
        
        if result:
            response = KnowledgeBaseQueryResponse(
                answer=result,
                bundle_id=bundle_id,
                query=request.query,
                mode=request.mode
            )
            return Success(data=response, msg="查询成功")
        else:
            return Fail(code=500, msg="查询失败，请检查知识库是否已构建")
            
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.error(f"Error querying knowledge base {bundle_id}: {e}")
        return Fail(code=500, msg=f"查询知识库时发生错误: {str(e)}")


@router.get("/bundles/{bundle_id}/status", summary="获取知识库状态")
async def get_knowledge_base_status(
    bundle_id: int = Path(..., description="Bundle ID", ge=1)
):
    """
    获取指定Bundle的知识库状态
    
    - **bundle_id**: Bundle ID，必须是knowledge_base类型
    """
    try:
        # 验证Bundle存在且类型正确
        bundle = await bundle_controller.get_bundle_info(bundle_id)
        if bundle.bundle_type != BundleType.KNOWLEDGE_BASE:
            return Fail(code=400, msg=f"Bundle {bundle_id} is not a knowledge base type")
        
        # 获取状态信息
        metadata = bundle.custom_metadata or {}
        
        response = KnowledgeBaseStatusResponse(
            bundle_id=bundle_id,
            status=metadata.get("status", "not_built"),
            last_build_at=metadata.get("last_build_at"),
            document_count=metadata.get("document_count", 0),
            error=metadata.get("error")
        )
        
        return Success(data=response, msg="获取状态成功")
        
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.error(f"Error getting knowledge base status for bundle {bundle_id}: {e}")
        return Fail(code=500, msg=f"获取知识库状态时发生错误: {str(e)}")


@router.delete("/bundles/{bundle_id}/clear", summary="清理知识库")
async def clear_knowledge_base(
    bundle_id: int = Path(..., description="Bundle ID", ge=1)
):
    """
    清理指定Bundle的知识库实例和数据
    
    - **bundle_id**: Bundle ID，必须是knowledge_base类型
    """
    try:
        # 验证Bundle存在且类型正确
        bundle = await bundle_controller.get_bundle_info(bundle_id)
        if bundle.bundle_type != BundleType.KNOWLEDGE_BASE:
            return Fail(code=400, msg=f"Bundle {bundle_id} is not a knowledge base type")
        
        # 清理RAG引擎实例
        await KnowledgeBaseManager.remove_instance(bundle_id)
        
        # 重置Bundle状态
        from app.models.bundle import Bundle
        metadata = bundle.custom_metadata or {}
        metadata.update({
            "status": "not_built",
            "last_build_at": None,
            "document_count": 0,
            "error": None
        })
        
        await Bundle.filter(id=bundle_id).update(custom_metadata=metadata)
        
        return Success(msg="知识库清理成功")
        
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.error(f"Error clearing knowledge base for bundle {bundle_id}: {e}")
        return Fail(code=500, msg=f"清理知识库时发生错误: {str(e)}")
