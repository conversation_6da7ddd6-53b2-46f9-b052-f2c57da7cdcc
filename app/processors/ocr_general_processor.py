"""
General OCR processor for page processing using vision LLM.
"""
from __future__ import annotations
from typing import Dict, Any, Union
import base64
import json

# Application imports
from app.log import logger
from app.ai.llms.llm import get_llm_by_type
from app.ai.prompts.template import get_prompt_template
from app.processors.base import BaseProcessor

# Type aliases
JSONDict = Dict[str, Any]


class GeneralOcrProcessor(BaseProcessor):
    """
    OCR processor for general bundle type using LLM vision models.
    Uses simpler OCR processing compared to education bundle.
    """
    # General OCR prompt template name
    GENERAL_OCR_TEMPLATE = "ocr_puretext"

    def __init__(self, template_name: str = GENERAL_OCR_TEMPLATE):
        """Initialize the processor."""
        self.template_name = template_name
        super().__init__()

    async def process(self, bucket: str, object_key: str, **kwargs) -> Union[Dict[str, Any], str, None]:
        """
        Process OCR on an image stored in MinIO using vision LLM.

        Args:
            bucket: MinIO bucket name
            object_key: Object key in the bucket
            **kwargs: Additional parameters (not used for general processing)

        Returns:
            Parsed JSON result from OCR analysis

        Raises:
            Exception: If OCR processing fails at any stage
        """
        try:
            # Get image data from MinIO using base class utility
            image_data = await self._get_image_from_minio(bucket, object_key)

            # Perform OCR on the image data using vision LLM
            logger.debug("Starting general OCR processing")
            parsed_result = await self._perform_ocr_with_vision_llm(image_data)

            logger.debug("General OCR processing completed")
            return parsed_result

        except Exception as e:
            logger.error(f"Error performing general OCR from MinIO: {e}")
            raise

    async def _perform_ocr_with_vision_llm(self, image_data: bytes, **kwargs) -> JSONDict:
        """
        Internal method to perform OCR on an image using vision LLM.

        Args:
            image_data: Raw image data as bytes

        Returns:
            Parsed JSON result from OCR

        Raises:
            Exception: If OCR processing fails
        """
        try:
            # Get vision LLM
            vision_llm = get_llm_by_type("vision")

            # Convert image to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')

            # Determine MIME type based on image header
            mime_type = self._determine_mime_type(image_data)

            template_name = kwargs.get('prompt_template', self.GENERAL_OCR_TEMPLATE)
            try:
                prompt_content = get_prompt_template(template_name)
            except:
                # Fallback to a simple prompt if template doesn't exist
                prompt_content = "Please extract all text from this image and return it as plain text."

            # Prepare messages for vision LLM
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt_content
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"{mime_type},{base64_image}"
                            }
                        }
                    ]
                }
            ]

            # Call vision LLM
            logger.debug("Calling vision LLM for general OCR analysis...")
            response = await vision_llm.ainvoke(messages)
            cleaned_response =  self._extract_code_block(response.content)
            return cleaned_response

        except Exception as e:
            logger.error(f"Error performing general OCR with vision LLM: {e}")
            raise



